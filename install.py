#!/usr/bin/env python3
"""
Installation script for AI Quiz Generator
Automates the setup process
"""
import os
import sys
import subprocess
import shutil
from pathlib import Path

def run_command(command, cwd=None, description=""):
    """Run a command and handle errors"""
    if description:
        print(f"📦 {description}...")
    
    try:
        result = subprocess.run(
            command,
            shell=True,
            cwd=cwd,
            capture_output=True,
            text=True,
            timeout=300  # 5 minute timeout
        )
        
        if result.returncode == 0:
            print(f"✅ {description or 'Command'} completed")
            return True
        else:
            print(f"❌ {description or 'Command'} failed:")
            print(result.stderr)
            return False
            
    except subprocess.TimeoutExpired:
        print(f"❌ {description or 'Command'} timed out")
        return False
    except Exception as e:
        print(f"❌ {description or 'Command'} error: {e}")
        return False

def check_prerequisites():
    """Check if required software is installed"""
    print("🔍 Checking prerequisites...")
    
    # Check Python
    if sys.version_info < (3, 8):
        print("❌ Python 3.8+ required")
        return False
    print("✅ Python version OK")
    
    # Check pip
    if not shutil.which('pip'):
        print("❌ pip not found")
        return False
    print("✅ pip found")
    
    # Check Node.js
    if not shutil.which('node'):
        print("❌ Node.js not found")
        print("Please install Node.js from https://nodejs.org/")
        return False
    print("✅ Node.js found")
    
    # Check npm
    if not shutil.which('npm'):
        print("❌ npm not found")
        return False
    print("✅ npm found")
    
    # Check PostgreSQL
    if not shutil.which('psql'):
        print("⚠️ PostgreSQL not found in PATH")
        print("Please install PostgreSQL and ensure it's in your PATH")
        print("Or install it manually and create database 'quiz_generator'")
    else:
        print("✅ PostgreSQL found")
    
    return True

def install_backend():
    """Install backend dependencies"""
    print("\n🐍 Installing backend dependencies...")
    
    # Create virtual environment
    if not Path('backend/venv').exists():
        if not run_command(
            f"{sys.executable} -m venv venv",
            cwd="backend",
            description="Creating virtual environment"
        ):
            return False
    
    # Determine activation script
    if os.name == 'nt':  # Windows
        pip_path = "backend/venv/Scripts/pip"
        python_path = "backend/venv/Scripts/python"
    else:  # Unix/Linux/macOS
        pip_path = "backend/venv/bin/pip"
        python_path = "backend/venv/bin/python"
    
    # Upgrade pip
    if not run_command(
        f"{python_path} -m pip install --upgrade pip",
        description="Upgrading pip"
    ):
        return False
    
    # Install requirements
    if not run_command(
        f"{pip_path} install -r requirements.txt",
        cwd="backend",
        description="Installing Python packages"
    ):
        return False
    
    # Copy environment file
    if not Path('backend/.env').exists():
        if Path('backend/.env.example').exists():
            shutil.copy('backend/.env.example', 'backend/.env')
            print("✅ Environment file created")
        else:
            print("⚠️ No .env.example found")
    
    return True

def install_frontend():
    """Install frontend dependencies"""
    print("\n⚛️ Installing frontend dependencies...")
    
    return run_command(
        "npm install",
        cwd="frontend",
        description="Installing Node.js packages"
    )

def setup_database():
    """Setup database"""
    print("\n🗄️ Setting up database...")
    
    # Check if PostgreSQL is accessible
    if shutil.which('createdb'):
        # Try to create database
        result = subprocess.run(
            ['createdb', 'quiz_generator'],
            capture_output=True,
            text=True
        )
        
        if result.returncode == 0:
            print("✅ Database 'quiz_generator' created")
        elif 'already exists' in result.stderr:
            print("✅ Database 'quiz_generator' already exists")
        else:
            print(f"⚠️ Database creation failed: {result.stderr}")
            print("Please create database manually:")
            print("createdb quiz_generator")
    
    # Initialize database schema
    if Path('database/init.sql').exists():
        if shutil.which('psql'):
            result = subprocess.run(
                ['psql', '-d', 'quiz_generator', '-f', 'database/init.sql'],
                capture_output=True,
                text=True
            )
            
            if result.returncode == 0:
                print("✅ Database schema initialized")
                return True
            else:
                print(f"⚠️ Schema initialization failed: {result.stderr}")
                print("Please run manually:")
                print("psql -d quiz_generator -f database/init.sql")
        else:
            print("⚠️ psql not found, please initialize database manually:")
            print("psql -d quiz_generator -f database/init.sql")
    
    return True

def download_ai_models():
    """Download AI models"""
    print("\n🤖 Downloading AI models...")
    print("This may take a few minutes on first run...")
    
    # Create a simple script to download models
    download_script = '''
import sys
sys.path.append("backend")
from transformers import T5ForConditionalGeneration, T5Tokenizer
import nltk

print("Downloading T5 model...")
model = T5ForConditionalGeneration.from_pretrained("t5-base")
tokenizer = T5Tokenizer.from_pretrained("t5-base")
print("T5 model downloaded successfully")

print("Downloading NLTK data...")
nltk.download('punkt', quiet=True)
nltk.download('stopwords', quiet=True)
nltk.download('averaged_perceptron_tagger', quiet=True)
print("NLTK data downloaded successfully")
'''
    
    # Write and run the script
    with open('download_models.py', 'w') as f:
        f.write(download_script)
    
    success = run_command(
        f"{sys.executable} download_models.py",
        description="Downloading AI models and NLTK data"
    )
    
    # Clean up
    if Path('download_models.py').exists():
        os.remove('download_models.py')
    
    return success

def main():
    """Main installation process"""
    print("🤖 AI Quiz Generator - Installation")
    print("=" * 50)
    
    # Check prerequisites
    if not check_prerequisites():
        print("\n❌ Prerequisites check failed")
        print("Please install missing software and try again")
        return False
    
    # Install backend
    if not install_backend():
        print("\n❌ Backend installation failed")
        return False
    
    # Install frontend
    if not install_frontend():
        print("\n❌ Frontend installation failed")
        return False
    
    # Setup database
    if not setup_database():
        print("\n❌ Database setup failed")
        return False
    
    # Download AI models
    if not download_ai_models():
        print("\n⚠️ AI model download failed")
        print("Models will be downloaded on first use")
    
    print("\n🎉 Installation completed successfully!")
    print("=" * 50)
    print("Next steps:")
    print("1. Ensure PostgreSQL is running")
    print("2. Run: python start.py")
    print("3. Open http://localhost:3000 in your browser")
    print("\nFor troubleshooting, run: python test_setup.py")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
