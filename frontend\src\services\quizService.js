import api from './api';

export const quizService = {
  // Upload PDF file
  uploadPDF: async (file) => {
    try {
      const formData = new FormData();
      formData.append('file', file);
      
      const response = await api.post('/upload/pdf', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.detail || 'File upload failed');
    }
  },

  // Get PDF info
  getPDFInfo: async (fileId) => {
    try {
      const response = await api.get(`/upload/pdf/${fileId}/info`);
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.detail || 'Failed to get PDF info');
    }
  },

  // Generate quiz
  generateQuiz: async (quizData) => {
    try {
      const response = await api.post('/quiz/generate', quizData);
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.detail || 'Quiz generation failed');
    }
  },

  // Get quiz
  getQuiz: async (quizId) => {
    try {
      const response = await api.get(`/quiz/${quizId}`);
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.detail || 'Failed to get quiz');
    }
  },

  // Submit quiz
  submitQuiz: async (submission) => {
    try {
      const response = await api.post('/quiz/submit', submission);
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.detail || 'Quiz submission failed');
    }
  },

  // Get quiz results
  getResults: async (resultId) => {
    try {
      const response = await api.get(`/quiz/results/${resultId}`);
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.detail || 'Failed to get results');
    }
  },

  // Delete PDF
  deletePDF: async (fileId) => {
    try {
      const response = await api.delete(`/upload/pdf/${fileId}`);
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.detail || 'Failed to delete file');
    }
  }
};
