import React from 'react';
import { Link } from 'react-router-dom';

const Dashboard = ({ user }) => {
  return (
    <div className="dashboard">
      <div className="welcome-section">
        <div className="card">
          <h1>Welcome back, {user.name}! 👋</h1>
          <p>Ready to generate some AI-powered quizzes from your textbooks?</p>
          
          <div className="quick-actions">
            <Link to="/generate" className="btn btn-primary">
              📚 Generate New Quiz
            </Link>
          </div>
        </div>
      </div>

      <div className="features-grid">
        <div className="card feature-card">
          <div className="feature-icon">📄</div>
          <h3>Upload PDF</h3>
          <p>Upload your textbook or study material in PDF format</p>
          <ul>
            <li>Support for any PDF textbook</li>
            <li>Automatic text extraction</li>
            <li>Chapter detection</li>
          </ul>
        </div>

        <div className="card feature-card">
          <div className="feature-icon">🎯</div>
          <h3>Choose Topic</h3>
          <p>Select specific chapters or keywords for focused learning</p>
          <ul>
            <li>Full document or specific topics</li>
            <li>Keyword-based filtering</li>
            <li>Smart content extraction</li>
          </ul>
        </div>

        <div className="card feature-card">
          <div className="feature-icon">⚡</div>
          <h3>AI Generation</h3>
          <p>Generate 50 high-quality multiple choice questions</p>
          <ul>
            <li>3 difficulty levels</li>
            <li>AI-powered question generation</li>
            <li>Instant quiz creation</li>
          </ul>
        </div>

        <div className="card feature-card">
          <div className="feature-icon">📊</div>
          <h3>Track Progress</h3>
          <p>Monitor your learning progress and quiz results</p>
          <ul>
            <li>Detailed score analysis</li>
            <li>Performance tracking</li>
            <li>Study recommendations</li>
          </ul>
        </div>
      </div>

      <div className="how-it-works">
        <div className="card">
          <h2>How It Works</h2>
          <div className="steps">
            <div className="step">
              <div className="step-number">1</div>
              <div className="step-content">
                <h4>Upload Your PDF</h4>
                <p>Upload any textbook or study material in PDF format</p>
              </div>
            </div>
            
            <div className="step">
              <div className="step-number">2</div>
              <div className="step-content">
                <h4>Select Topic & Difficulty</h4>
                <p>Choose specific chapters or keywords and set difficulty level</p>
              </div>
            </div>
            
            <div className="step">
              <div className="step-number">3</div>
              <div className="step-content">
                <h4>Generate Quiz</h4>
                <p>AI creates 50 multiple choice questions from your content</p>
              </div>
            </div>
            
            <div className="step">
              <div className="step-number">4</div>
              <div className="step-content">
                <h4>Take Quiz & Review</h4>
                <p>Answer questions and get detailed results with explanations</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <style jsx>{`
        .dashboard {
          max-width: 1200px;
          margin: 0 auto;
          padding: 20px;
        }

        .welcome-section {
          margin-bottom: 3rem;
        }

        .welcome-section h1 {
          font-size: 2.5rem;
          margin-bottom: 1rem;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }

        .welcome-section p {
          font-size: 1.2rem;
          color: #666;
          margin-bottom: 2rem;
        }

        .quick-actions {
          display: flex;
          gap: 1rem;
        }

        .features-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
          gap: 2rem;
          margin-bottom: 3rem;
        }

        .feature-card {
          text-align: center;
          transition: transform 0.3s ease;
        }

        .feature-card:hover {
          transform: translateY(-5px);
        }

        .feature-icon {
          font-size: 3rem;
          margin-bottom: 1rem;
        }

        .feature-card h3 {
          margin-bottom: 1rem;
          color: #333;
        }

        .feature-card p {
          color: #666;
          margin-bottom: 1rem;
        }

        .feature-card ul {
          list-style: none;
          padding: 0;
          text-align: left;
        }

        .feature-card li {
          padding: 0.25rem 0;
          color: #555;
        }

        .feature-card li:before {
          content: "✓ ";
          color: #28a745;
          font-weight: bold;
        }

        .how-it-works h2 {
          text-align: center;
          margin-bottom: 2rem;
          color: #333;
        }

        .steps {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
          gap: 2rem;
        }

        .step {
          display: flex;
          align-items: flex-start;
          gap: 1rem;
        }

        .step-number {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          color: white;
          width: 40px;
          height: 40px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-weight: bold;
          flex-shrink: 0;
        }

        .step-content h4 {
          margin-bottom: 0.5rem;
          color: #333;
        }

        .step-content p {
          color: #666;
          font-size: 0.9rem;
        }

        @media (max-width: 768px) {
          .welcome-section h1 {
            font-size: 2rem;
          }
          
          .features-grid {
            grid-template-columns: 1fr;
          }
          
          .steps {
            grid-template-columns: 1fr;
          }
        }
      `}</style>
    </div>
  );
};

export default Dashboard;
