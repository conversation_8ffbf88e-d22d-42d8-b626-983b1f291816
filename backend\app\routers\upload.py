"""
File upload router for PDF processing
"""
import os
import sys
import uuid
from fastapi import APIRouter, Depends, HTTPException, UploadFile, File
from sqlalchemy.orm import Session
from pydantic import BaseModel
from typing import List

# Add parent directories to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from database.connection import get_db
from models.database import Student
from app.routers.auth import get_current_user
from utils.pdf_processor import PDFProcessor

router = APIRouter()

class UploadResponse(BaseModel):
    filename: str
    file_id: str
    size: int
    chapters: List[str]
    keywords: List[str]

class PDFInfo(BaseModel):
    filename: str
    chapters: List[str]
    keywords: List[str]
    total_pages: int
    text_preview: str

# Configuration
MAX_FILE_SIZE = int(os.getenv("MAX_FILE_SIZE_MB", 50)) * 1024 * 1024  # 50MB default
ALLOWED_EXTENSIONS = {"pdf"}
UPLOAD_FOLDER = os.getenv("UPLOAD_FOLDER", "uploads")

def allowed_file(filename: str) -> bool:
    """Check if file extension is allowed"""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

@router.post("/pdf", response_model=UploadResponse)
async def upload_pdf(
    file: UploadFile = File(...),
    current_user: Student = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Upload and process PDF file"""
    
    # Validate file
    if not file.filename:
        raise HTTPException(status_code=400, detail="No file selected")
    
    if not allowed_file(file.filename):
        raise HTTPException(
            status_code=400, 
            detail="Only PDF files are allowed"
        )
    
    # Check file size
    file_content = await file.read()
    if len(file_content) > MAX_FILE_SIZE:
        raise HTTPException(
            status_code=413,
            detail=f"File too large. Maximum size is {MAX_FILE_SIZE // (1024*1024)}MB"
        )
    
    # Generate unique filename
    file_id = str(uuid.uuid4())
    file_extension = file.filename.rsplit('.', 1)[1].lower()
    unique_filename = f"{file_id}.{file_extension}"
    file_path = os.path.join(UPLOAD_FOLDER, unique_filename)
    
    # Save file
    try:
        os.makedirs(UPLOAD_FOLDER, exist_ok=True)
        with open(file_path, "wb") as buffer:
            buffer.write(file_content)
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to save file: {str(e)}"
        )
    
    # Process PDF
    try:
        processor = PDFProcessor()
        pdf_info = processor.process_pdf(file_path)
        
        return UploadResponse(
            filename=file.filename,
            file_id=file_id,
            size=len(file_content),
            chapters=pdf_info["chapters"],
            keywords=pdf_info["keywords"]
        )
    except Exception as e:
        # Clean up file if processing fails
        if os.path.exists(file_path):
            os.remove(file_path)
        raise HTTPException(
            status_code=500,
            detail=f"Failed to process PDF: {str(e)}"
        )

@router.get("/pdf/{file_id}/info", response_model=PDFInfo)
async def get_pdf_info(
    file_id: str,
    current_user: Student = Depends(get_current_user)
):
    """Get information about uploaded PDF"""
    
    file_path = os.path.join(UPLOAD_FOLDER, f"{file_id}.pdf")
    
    if not os.path.exists(file_path):
        raise HTTPException(status_code=404, detail="File not found")
    
    try:
        processor = PDFProcessor()
        pdf_info = processor.process_pdf(file_path)
        
        return PDFInfo(
            filename=f"{file_id}.pdf",
            chapters=pdf_info["chapters"],
            keywords=pdf_info["keywords"],
            total_pages=pdf_info["total_pages"],
            text_preview=pdf_info["text_preview"][:500] + "..." if len(pdf_info["text_preview"]) > 500 else pdf_info["text_preview"]
        )
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get PDF info: {str(e)}"
        )

@router.delete("/pdf/{file_id}")
async def delete_pdf(
    file_id: str,
    current_user: Student = Depends(get_current_user)
):
    """Delete uploaded PDF file"""
    
    file_path = os.path.join(UPLOAD_FOLDER, f"{file_id}.pdf")
    
    if not os.path.exists(file_path):
        raise HTTPException(status_code=404, detail="File not found")
    
    try:
        os.remove(file_path)
        return {"message": "File deleted successfully"}
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to delete file: {str(e)}"
        )
