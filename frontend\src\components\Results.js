import React, { useState, useEffect } from 'react';
import { use<PERSON>ara<PERSON>, Link } from 'react-router-dom';
import { quizService } from '../services/quizService';

const Results = ({ user }) => {
  const { resultId } = useParams();
  const [result, setResult] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [showDetails, setShowDetails] = useState(false);

  useEffect(() => {
    loadResults();
  }, [resultId]);

  const loadResults = async () => {
    try {
      const resultData = await quizService.getResults(resultId);
      setResult(resultData);
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const getScoreColor = (score) => {
    if (score >= 80) return '#28a745';
    if (score >= 60) return '#ffc107';
    return '#dc3545';
  };

  const getGrade = (score) => {
    if (score >= 90) return 'A';
    if (score >= 80) return 'B';
    if (score >= 70) return 'C';
    if (score >= 60) return 'D';
    return 'F';
  };

  const formatTime = (seconds) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}m ${remainingSeconds}s`;
  };

  if (loading) {
    return (
      <div className="loading">
        <div className="spinner"></div>
        <p>Loading results...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="card">
        <div className="alert alert-error">
          {error}
        </div>
      </div>
    );
  }

  if (!result) {
    return (
      <div className="card">
        <div className="alert alert-error">
          Results not found
        </div>
      </div>
    );
  }

  return (
    <div className="results">
      {/* Score Summary */}
      <div className="card score-card">
        <div className="score-header">
          <h1>🎉 Quiz Results</h1>
          <div className="score-circle" style={{ borderColor: getScoreColor(result.score) }}>
            <div className="score-value" style={{ color: getScoreColor(result.score) }}>
              {result.score.toFixed(1)}%
            </div>
            <div className="score-grade" style={{ color: getScoreColor(result.score) }}>
              Grade {getGrade(result.score)}
            </div>
          </div>
        </div>

        <div className="score-stats">
          <div className="stat">
            <div className="stat-value text-success">{result.correct_count}</div>
            <div className="stat-label">Correct</div>
          </div>
          <div className="stat">
            <div className="stat-value text-danger">{result.wrong_count}</div>
            <div className="stat-label">Wrong</div>
          </div>
          <div className="stat">
            <div className="stat-value">{result.total_questions}</div>
            <div className="stat-label">Total</div>
          </div>
          <div className="stat">
            <div className="stat-value">{formatTime(result.time_taken_seconds)}</div>
            <div className="stat-label">Time</div>
          </div>
        </div>

        <div className="performance-message">
          {result.score >= 80 ? (
            <div className="message success">
              <strong>Excellent work! 🌟</strong>
              <p>You have a strong understanding of the material.</p>
            </div>
          ) : result.score >= 60 ? (
            <div className="message warning">
              <strong>Good effort! 📚</strong>
              <p>You're on the right track. Review the incorrect answers to improve.</p>
            </div>
          ) : (
            <div className="message danger">
              <strong>Keep studying! 💪</strong>
              <p>Focus on the areas where you got questions wrong and try again.</p>
            </div>
          )}
        </div>

        <div className="result-actions">
          <button
            onClick={() => setShowDetails(!showDetails)}
            className="btn btn-primary"
          >
            {showDetails ? 'Hide Details' : 'View Detailed Results'}
          </button>
          <Link to="/generate" className="btn btn-secondary">
            Generate New Quiz
          </Link>
          <Link to="/dashboard" className="btn btn-secondary">
            Back to Dashboard
          </Link>
        </div>
      </div>

      {/* Detailed Results */}
      {showDetails && (
        <div className="card details-card">
          <h2>📋 Detailed Results</h2>
          <p>Review your answers and explanations for each question.</p>

          <div className="questions-review">
            {result.detailed_results.map((item, index) => (
              <div key={item.question_id} className="question-review">
                <div className="question-header">
                  <span className="question-number">Question {index + 1}</span>
                  <span className={`result-badge ${item.is_correct ? 'correct' : 'incorrect'}`}>
                    {item.is_correct ? '✓ Correct' : '✗ Incorrect'}
                  </span>
                </div>

                <div className="question-text">
                  {item.question_text}
                </div>

                <div className="answer-comparison">
                  <div className="answer-row">
                    <span className="answer-label">Your Answer:</span>
                    <span className={`answer-value ${item.is_correct ? 'correct' : 'incorrect'}`}>
                      {item.selected_answer}
                    </span>
                  </div>
                  {!item.is_correct && (
                    <div className="answer-row">
                      <span className="answer-label">Correct Answer:</span>
                      <span className="answer-value correct">
                        {item.correct_answer}
                      </span>
                    </div>
                  )}
                </div>

                {item.explanation && (
                  <div className="explanation">
                    <strong>Explanation:</strong>
                    <p>{item.explanation}</p>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      )}

      <style jsx>{`
        .results {
          max-width: 900px;
          margin: 0 auto;
          padding: 20px;
        }

        .score-card {
          text-align: center;
          margin-bottom: 2rem;
        }

        .score-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 2rem;
        }

        .score-header h1 {
          margin: 0;
          color: #333;
        }

        .score-circle {
          width: 120px;
          height: 120px;
          border: 4px solid;
          border-radius: 50%;
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          background: white;
        }

        .score-value {
          font-size: 2rem;
          font-weight: bold;
          line-height: 1;
        }

        .score-grade {
          font-size: 1rem;
          font-weight: bold;
        }

        .score-stats {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
          gap: 1rem;
          margin-bottom: 2rem;
        }

        .stat {
          text-align: center;
        }

        .stat-value {
          font-size: 2rem;
          font-weight: bold;
          margin-bottom: 0.5rem;
        }

        .stat-label {
          color: #666;
          font-size: 0.9rem;
        }

        .performance-message {
          margin-bottom: 2rem;
        }

        .message {
          padding: 1.5rem;
          border-radius: 8px;
          text-align: left;
        }

        .message.success {
          background: #d4edda;
          border: 1px solid #c3e6cb;
          color: #155724;
        }

        .message.warning {
          background: #fff3cd;
          border: 1px solid #ffeaa7;
          color: #856404;
        }

        .message.danger {
          background: #f8d7da;
          border: 1px solid #f5c6cb;
          color: #721c24;
        }

        .message p {
          margin: 0.5rem 0 0 0;
        }

        .result-actions {
          display: flex;
          gap: 1rem;
          justify-content: center;
          flex-wrap: wrap;
        }

        .details-card {
          margin-top: 2rem;
        }

        .details-card h2 {
          margin-bottom: 0.5rem;
          color: #333;
        }

        .details-card p {
          color: #666;
          margin-bottom: 2rem;
        }

        .questions-review {
          display: flex;
          flex-direction: column;
          gap: 2rem;
        }

        .question-review {
          border: 1px solid #e9ecef;
          border-radius: 8px;
          padding: 1.5rem;
        }

        .question-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 1rem;
        }

        .question-number {
          font-weight: bold;
          color: #333;
        }

        .result-badge {
          padding: 0.25rem 0.75rem;
          border-radius: 20px;
          font-size: 0.9rem;
          font-weight: bold;
        }

        .result-badge.correct {
          background: #d4edda;
          color: #155724;
        }

        .result-badge.incorrect {
          background: #f8d7da;
          color: #721c24;
        }

        .question-text {
          margin-bottom: 1rem;
          font-weight: 500;
          line-height: 1.5;
        }

        .answer-comparison {
          margin-bottom: 1rem;
        }

        .answer-row {
          display: flex;
          gap: 1rem;
          margin-bottom: 0.5rem;
        }

        .answer-label {
          font-weight: bold;
          min-width: 120px;
        }

        .answer-value {
          padding: 0.25rem 0.5rem;
          border-radius: 4px;
          font-weight: bold;
        }

        .answer-value.correct {
          background: #d4edda;
          color: #155724;
        }

        .answer-value.incorrect {
          background: #f8d7da;
          color: #721c24;
        }

        .explanation {
          background: #f8f9fa;
          padding: 1rem;
          border-radius: 6px;
          border-left: 4px solid #667eea;
        }

        .explanation p {
          margin: 0.5rem 0 0 0;
          color: #555;
        }

        @media (max-width: 768px) {
          .score-header {
            flex-direction: column;
            gap: 1rem;
          }
          
          .result-actions {
            flex-direction: column;
          }
          
          .answer-row {
            flex-direction: column;
            gap: 0.25rem;
          }
          
          .answer-label {
            min-width: auto;
          }
        }
      `}</style>
    </div>
  );
};

export default Results;
