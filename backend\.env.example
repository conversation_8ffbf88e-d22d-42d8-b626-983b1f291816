# Database Configuration
DATABASE_URL=postgresql://postgres:password@localhost:5432/quiz_generator

# API Configuration
API_HOST=0.0.0.0
API_PORT=8000
DEBUG=True

# Security
SECRET_KEY=your-secret-key-here
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# AI Model Configuration
HUGGINGFACE_MODEL_NAME=t5-base
MAX_QUESTION_LENGTH=512
BATCH_SIZE=4

# File Upload Configuration
MAX_FILE_SIZE_MB=50
UPLOAD_FOLDER=uploads
ALLOWED_EXTENSIONS=pdf

# CORS Configuration
ALLOWED_ORIGINS=http://localhost:3000,http://127.0.0.1:3000
