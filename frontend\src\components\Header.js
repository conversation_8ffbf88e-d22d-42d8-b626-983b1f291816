import React from 'react';
import { Link } from 'react-router-dom';

const Header = ({ user, onLogout }) => {
  return (
    <header style={headerStyle}>
      <div className="container" style={containerStyle}>
        <Link to="/" style={logoStyle}>
          🤖 AI Quiz Generator
        </Link>
        
        <nav style={navStyle}>
          {user ? (
            <>
              <Link to="/dashboard" style={linkStyle}>
                Dashboard
              </Link>
              <Link to="/generate" style={linkStyle}>
                Generate Quiz
              </Link>
              <span style={userStyle}>
                Welcome, {user.name}
              </span>
              <button 
                onClick={onLogout}
                style={logoutButtonStyle}
              >
                Logout
              </button>
            </>
          ) : (
            <>
              <Link to="/login" style={linkStyle}>
                Login
              </Link>
              <Link to="/register" style={linkStyle}>
                Register
              </Link>
            </>
          )}
        </nav>
      </div>
    </header>
  );
};

const headerStyle = {
  background: 'rgba(255, 255, 255, 0.95)',
  backdropFilter: 'blur(10px)',
  borderBottom: '1px solid rgba(255, 255, 255, 0.2)',
  padding: '1rem 0',
  position: 'sticky',
  top: 0,
  zIndex: 1000,
  boxShadow: '0 2px 10px rgba(0, 0, 0, 0.1)'
};

const containerStyle = {
  display: 'flex',
  justifyContent: 'space-between',
  alignItems: 'center',
  maxWidth: '1200px',
  margin: '0 auto',
  padding: '0 20px'
};

const logoStyle = {
  fontSize: '1.5rem',
  fontWeight: 'bold',
  textDecoration: 'none',
  color: '#333',
  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
  WebkitBackgroundClip: 'text',
  WebkitTextFillColor: 'transparent'
};

const navStyle = {
  display: 'flex',
  alignItems: 'center',
  gap: '1rem'
};

const linkStyle = {
  textDecoration: 'none',
  color: '#333',
  fontWeight: '500',
  padding: '0.5rem 1rem',
  borderRadius: '6px',
  transition: 'background-color 0.3s ease'
};

const userStyle = {
  color: '#666',
  fontSize: '0.9rem',
  marginLeft: '1rem'
};

const logoutButtonStyle = {
  background: 'transparent',
  border: '1px solid #dc3545',
  color: '#dc3545',
  padding: '0.5rem 1rem',
  borderRadius: '6px',
  cursor: 'pointer',
  fontSize: '0.9rem',
  transition: 'all 0.3s ease'
};

// Add hover effects with CSS-in-JS alternative
const addHoverEffects = () => {
  const style = document.createElement('style');
  style.textContent = `
    .nav-link:hover {
      background-color: rgba(102, 126, 234, 0.1);
    }
    .logout-btn:hover {
      background-color: #dc3545;
      color: white;
    }
  `;
  document.head.appendChild(style);
};

// Call once when component mounts
if (typeof window !== 'undefined') {
  addHoverEffects();
}

export default Header;
