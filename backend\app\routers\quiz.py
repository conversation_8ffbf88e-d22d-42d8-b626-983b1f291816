"""
Quiz router for quiz generation and submission
"""
import os
import sys
from typing import List, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks
from sqlalchemy.orm import Session
from pydantic import BaseModel
from datetime import datetime

# Add parent directories to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from database.connection import get_db
from models.database import Student, Quiz, Question, Result, StudentAnswer
from app.routers.auth import get_current_user
from utils.pdf_processor import PDFProcessor
from utils.quiz_generator import QuizGenerator

router = APIRouter()

class QuizGenerateRequest(BaseModel):
    file_id: str
    topic: str
    difficulty: str
    num_questions: int = 50

class QuestionResponse(BaseModel):
    id: str
    question_number: int
    question_text: str
    option_a: str
    option_b: str
    option_c: str
    option_d: str

class QuizResponse(BaseModel):
    quiz_id: str
    topic: str
    difficulty: str
    total_questions: int
    questions: List[QuestionResponse]
    created_at: datetime

class AnswerSubmission(BaseModel):
    question_id: str
    selected_answer: str

class QuizSubmission(BaseModel):
    quiz_id: str
    answers: List[AnswerSubmission]
    time_taken_seconds: int = None

class ResultResponse(BaseModel):
    result_id: str
    quiz_id: str
    score: float
    correct_count: int
    wrong_count: int
    total_questions: int
    time_taken_seconds: int
    submitted_at: datetime
    detailed_results: List[Dict[str, Any]]

# Global quiz generator instance
quiz_generator = None

def get_quiz_generator():
    """Get or create quiz generator instance"""
    global quiz_generator
    if quiz_generator is None:
        model_name = os.getenv("HUGGINGFACE_MODEL_NAME", "t5-base")
        quiz_generator = QuizGenerator(model_name)
    return quiz_generator

@router.post("/generate", response_model=QuizResponse)
async def generate_quiz(
    request: QuizGenerateRequest,
    background_tasks: BackgroundTasks,
    current_user: Student = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Generate a new quiz from uploaded PDF"""
    
    # Validate difficulty
    if request.difficulty not in ["Easy", "Medium", "Hard"]:
        raise HTTPException(status_code=400, detail="Invalid difficulty level")
    
    # Validate number of questions
    if request.num_questions < 1 or request.num_questions > 100:
        raise HTTPException(status_code=400, detail="Number of questions must be between 1 and 100")
    
    # Check if PDF file exists
    upload_folder = os.getenv("UPLOAD_FOLDER", "uploads")
    file_path = os.path.join(upload_folder, f"{request.file_id}.pdf")
    
    if not os.path.exists(file_path):
        raise HTTPException(status_code=404, detail="PDF file not found")
    
    try:
        # Process PDF
        pdf_processor = PDFProcessor()
        pdf_data = pdf_processor.process_pdf(file_path)
        
        # Extract relevant text for the topic
        if request.topic and request.topic.lower() != "full document":
            relevant_text = pdf_processor.extract_text_by_topic(
                pdf_data["cleaned_text"], 
                request.topic
            )
            if not relevant_text:
                relevant_text = pdf_data["cleaned_text"]
        else:
            relevant_text = pdf_data["cleaned_text"]
        
        if len(relevant_text.strip()) < 100:
            raise HTTPException(
                status_code=400, 
                detail="Not enough text content for the selected topic"
            )
        
        # Create quiz record
        db_quiz = Quiz(
            student_id=current_user.id,
            topic=request.topic,
            difficulty=request.difficulty,
            pdf_filename=f"{request.file_id}.pdf",
            total_questions=request.num_questions
        )
        db.add(db_quiz)
        db.commit()
        db.refresh(db_quiz)
        
        # Generate questions in background
        background_tasks.add_task(
            generate_questions_background,
            str(db_quiz.id),
            relevant_text,
            request.topic,
            request.difficulty,
            request.num_questions
        )
        
        # Return initial response
        return QuizResponse(
            quiz_id=str(db_quiz.id),
            topic=db_quiz.topic,
            difficulty=db_quiz.difficulty,
            total_questions=db_quiz.total_questions,
            questions=[],  # Questions will be generated in background
            created_at=db_quiz.created_at
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to generate quiz: {str(e)}"
        )

async def generate_questions_background(
    quiz_id: str,
    text: str,
    topic: str,
    difficulty: str,
    num_questions: int
):
    """Background task to generate questions"""
    try:
        # Get quiz generator
        generator = get_quiz_generator()
        
        # Generate questions
        questions_data = generator.generate_quiz(
            text=text,
            topic=topic,
            difficulty=difficulty,
            num_questions=num_questions
        )
        
        # Save questions to database
        from database.connection import SessionLocal
        db = SessionLocal()
        
        try:
            for q_data in questions_data:
                db_question = Question(
                    quiz_id=quiz_id,
                    question_number=q_data["question_number"],
                    question_text=q_data["question"],
                    option_a=q_data["options"][0],
                    option_b=q_data["options"][1],
                    option_c=q_data["options"][2],
                    option_d=q_data["options"][3],
                    correct_answer=q_data["correct_answer"],
                    explanation=q_data.get("explanation", "")
                )
                db.add(db_question)
            
            db.commit()
            print(f"✅ Saved {len(questions_data)} questions for quiz {quiz_id}")
            
        finally:
            db.close()
            
    except Exception as e:
        print(f"❌ Background question generation failed: {e}")

@router.get("/{quiz_id}", response_model=QuizResponse)
async def get_quiz(
    quiz_id: str,
    current_user: Student = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get quiz with questions"""
    
    # Get quiz
    quiz = db.query(Quiz).filter(
        Quiz.id == quiz_id,
        Quiz.student_id == current_user.id
    ).first()
    
    if not quiz:
        raise HTTPException(status_code=404, detail="Quiz not found")
    
    # Get questions
    questions = db.query(Question).filter(
        Question.quiz_id == quiz_id
    ).order_by(Question.question_number).all()
    
    question_responses = [
        QuestionResponse(
            id=str(q.id),
            question_number=q.question_number,
            question_text=q.question_text,
            option_a=q.option_a,
            option_b=q.option_b,
            option_c=q.option_c,
            option_d=q.option_d
        )
        for q in questions
    ]
    
    return QuizResponse(
        quiz_id=str(quiz.id),
        topic=quiz.topic,
        difficulty=quiz.difficulty,
        total_questions=quiz.total_questions,
        questions=question_responses,
        created_at=quiz.created_at
    )

@router.post("/submit", response_model=ResultResponse)
async def submit_quiz(
    submission: QuizSubmission,
    current_user: Student = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Submit quiz answers and get results"""
    
    # Get quiz
    quiz = db.query(Quiz).filter(
        Quiz.id == submission.quiz_id,
        Quiz.student_id == current_user.id
    ).first()
    
    if not quiz:
        raise HTTPException(status_code=404, detail="Quiz not found")
    
    # Get questions
    questions = db.query(Question).filter(Question.quiz_id == submission.quiz_id).all()
    question_dict = {str(q.id): q for q in questions}
    
    # Calculate results
    correct_count = 0
    wrong_count = 0
    detailed_results = []
    
    # Create result record
    db_result = Result(
        quiz_id=submission.quiz_id,
        student_id=current_user.id,
        score=0.0,  # Will be updated
        correct_count=0,  # Will be updated
        wrong_count=0,  # Will be updated
        total_questions=len(questions),
        time_taken_seconds=submission.time_taken_seconds
    )
    db.add(db_result)
    db.commit()
    db.refresh(db_result)
    
    # Process answers
    for answer in submission.answers:
        question = question_dict.get(answer.question_id)
        if not question:
            continue
        
        is_correct = answer.selected_answer.upper() == question.correct_answer.upper()
        
        if is_correct:
            correct_count += 1
        else:
            wrong_count += 1
        
        # Save student answer
        db_answer = StudentAnswer(
            result_id=db_result.id,
            question_id=answer.question_id,
            selected_answer=answer.selected_answer.upper(),
            is_correct=is_correct
        )
        db.add(db_answer)
        
        # Add to detailed results
        detailed_results.append({
            "question_id": answer.question_id,
            "question_text": question.question_text,
            "selected_answer": answer.selected_answer.upper(),
            "correct_answer": question.correct_answer,
            "is_correct": is_correct,
            "explanation": question.explanation
        })
    
    # Calculate final score
    score = (correct_count / len(questions)) * 100 if questions else 0
    
    # Update result
    db_result.score = round(score, 2)
    db_result.correct_count = correct_count
    db_result.wrong_count = wrong_count
    
    db.commit()
    
    return ResultResponse(
        result_id=str(db_result.id),
        quiz_id=str(db_result.quiz_id),
        score=db_result.score,
        correct_count=db_result.correct_count,
        wrong_count=db_result.wrong_count,
        total_questions=db_result.total_questions,
        time_taken_seconds=db_result.time_taken_seconds or 0,
        submitted_at=db_result.submitted_at,
        detailed_results=detailed_results
    )

@router.get("/results/{result_id}", response_model=ResultResponse)
async def get_quiz_result(
    result_id: str,
    current_user: Student = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get detailed quiz results"""
    
    # Get result
    result = db.query(Result).filter(
        Result.id == result_id,
        Result.student_id == current_user.id
    ).first()
    
    if not result:
        raise HTTPException(status_code=404, detail="Result not found")
    
    # Get detailed answers
    answers = db.query(StudentAnswer, Question).join(
        Question, StudentAnswer.question_id == Question.id
    ).filter(StudentAnswer.result_id == result_id).all()
    
    detailed_results = [
        {
            "question_id": str(answer.StudentAnswer.question_id),
            "question_text": answer.Question.question_text,
            "selected_answer": answer.StudentAnswer.selected_answer,
            "correct_answer": answer.Question.correct_answer,
            "is_correct": answer.StudentAnswer.is_correct,
            "explanation": answer.Question.explanation
        }
        for answer in answers
    ]
    
    return ResultResponse(
        result_id=str(result.id),
        quiz_id=str(result.quiz_id),
        score=result.score,
        correct_count=result.correct_count,
        wrong_count=result.wrong_count,
        total_questions=result.total_questions,
        time_taken_seconds=result.time_taken_seconds or 0,
        submitted_at=result.submitted_at,
        detailed_results=detailed_results
    )
