"""
PDF processing utilities for text extraction and analysis
"""
import re
import os
from typing import List, Dict, Any
import PyPDF2
import pdfplumber
from collections import Counter
import nltk
from nltk.corpus import stopwords
from nltk.tokenize import word_tokenize, sent_tokenize
from nltk.tag import pos_tag

class PDFProcessor:
    def __init__(self):
        """Initialize PDF processor with NLTK data"""
        self._download_nltk_data()
        self.stop_words = set(stopwords.words('english'))
        
    def _download_nltk_data(self):
        """Download required NLTK data"""
        try:
            nltk.data.find('tokenizers/punkt')
        except LookupError:
            nltk.download('punkt')
            
        try:
            nltk.data.find('corpora/stopwords')
        except LookupError:
            nltk.download('stopwords')
            
        try:
            nltk.data.find('taggers/averaged_perceptron_tagger')
        except LookupError:
            nltk.download('averaged_perceptron_tagger')
    
    def extract_text_pypdf2(self, file_path: str) -> str:
        """Extract text using PyPDF2"""
        text = ""
        try:
            with open(file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                for page in pdf_reader.pages:
                    text += page.extract_text() + "\n"
        except Exception as e:
            raise Exception(f"PyPDF2 extraction failed: {str(e)}")
        return text
    
    def extract_text_pdfplumber(self, file_path: str) -> str:
        """Extract text using pdfplumber (more accurate)"""
        text = ""
        try:
            with pdfplumber.open(file_path) as pdf:
                for page in pdf.pages:
                    page_text = page.extract_text()
                    if page_text:
                        text += page_text + "\n"
        except Exception as e:
            raise Exception(f"pdfplumber extraction failed: {str(e)}")
        return text
    
    def extract_text(self, file_path: str) -> str:
        """Extract text using the best available method"""
        # Try pdfplumber first (more accurate)
        try:
            text = self.extract_text_pdfplumber(file_path)
            if text.strip():
                return text
        except:
            pass
        
        # Fallback to PyPDF2
        try:
            text = self.extract_text_pypdf2(file_path)
            if text.strip():
                return text
        except:
            pass
        
        raise Exception("Failed to extract text from PDF")
    
    def clean_text(self, text: str) -> str:
        """Clean and preprocess text"""
        # Remove extra whitespace and normalize
        text = re.sub(r'\s+', ' ', text)
        
        # Remove page numbers and headers/footers patterns
        text = re.sub(r'\n\d+\n', '\n', text)
        text = re.sub(r'\n[A-Z\s]+\n', '\n', text)
        
        # Remove special characters but keep punctuation
        text = re.sub(r'[^\w\s\.\,\!\?\;\:\-\(\)]', ' ', text)
        
        return text.strip()
    
    def extract_chapters(self, text: str) -> List[str]:
        """Extract chapter titles from text"""
        chapters = []
        
        # Common chapter patterns
        patterns = [
            r'Chapter\s+\d+[:\.\-\s]+([^\n]+)',
            r'CHAPTER\s+\d+[:\.\-\s]+([^\n]+)',
            r'Chapter\s+[IVX]+[:\.\-\s]+([^\n]+)',
            r'CHAPTER\s+[IVX]+[:\.\-\s]+([^\n]+)',
            r'\n\d+\.\s+([A-Z][^\n]{10,80})\n',
            r'\n\d+\s+([A-Z][A-Z\s]{5,50})\n'
        ]
        
        for pattern in patterns:
            matches = re.finditer(pattern, text, re.IGNORECASE | re.MULTILINE)
            for match in matches:
                chapter_title = match.group(1).strip()
                if len(chapter_title) > 5 and len(chapter_title) < 100:
                    chapters.append(chapter_title)
        
        # Remove duplicates while preserving order
        seen = set()
        unique_chapters = []
        for chapter in chapters:
            if chapter.lower() not in seen:
                seen.add(chapter.lower())
                unique_chapters.append(chapter)
        
        return unique_chapters[:20]  # Limit to 20 chapters
    
    def extract_keywords(self, text: str, max_keywords: int = 50) -> List[str]:
        """Extract important keywords from text"""
        # Tokenize and clean
        words = word_tokenize(text.lower())
        
        # Filter words
        filtered_words = [
            word for word in words 
            if word.isalpha() 
            and len(word) > 3 
            and word not in self.stop_words
        ]
        
        # Get POS tags to focus on nouns and adjectives
        pos_tags = pos_tag(filtered_words)
        important_words = [
            word for word, pos in pos_tags 
            if pos.startswith(('NN', 'JJ', 'VB'))  # Nouns, adjectives, verbs
        ]
        
        # Count frequency
        word_freq = Counter(important_words)
        
        # Get most common words
        keywords = [word for word, freq in word_freq.most_common(max_keywords)]
        
        return keywords
    
    def get_page_count(self, file_path: str) -> int:
        """Get number of pages in PDF"""
        try:
            with open(file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                return len(pdf_reader.pages)
        except:
            return 0
    
    def process_pdf(self, file_path: str) -> Dict[str, Any]:
        """Process PDF and extract all information"""
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"PDF file not found: {file_path}")
        
        # Extract text
        raw_text = self.extract_text(file_path)
        cleaned_text = self.clean_text(raw_text)
        
        # Extract chapters and keywords
        chapters = self.extract_chapters(raw_text)
        keywords = self.extract_keywords(cleaned_text)
        
        # Get page count
        page_count = self.get_page_count(file_path)
        
        return {
            "raw_text": raw_text,
            "cleaned_text": cleaned_text,
            "chapters": chapters,
            "keywords": keywords,
            "total_pages": page_count,
            "text_preview": cleaned_text[:1000]  # First 1000 characters
        }
    
    def extract_text_by_topic(self, text: str, topic: str, context_sentences: int = 5) -> str:
        """Extract text related to a specific topic/keyword"""
        sentences = sent_tokenize(text)
        relevant_sentences = []
        
        topic_lower = topic.lower()
        
        for i, sentence in enumerate(sentences):
            if topic_lower in sentence.lower():
                # Add context sentences before and after
                start_idx = max(0, i - context_sentences)
                end_idx = min(len(sentences), i + context_sentences + 1)
                
                context = sentences[start_idx:end_idx]
                relevant_sentences.extend(context)
        
        # Remove duplicates while preserving order
        seen = set()
        unique_sentences = []
        for sentence in relevant_sentences:
            if sentence not in seen:
                seen.add(sentence)
                unique_sentences.append(sentence)
        
        return " ".join(unique_sentences)
