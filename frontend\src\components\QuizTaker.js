import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { quizService } from '../services/quizService';

const QuizTaker = ({ user }) => {
  const { quizId } = useParams();
  const navigate = useNavigate();
  
  const [quiz, setQuiz] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [currentQuestion, setCurrentQuestion] = useState(0);
  const [answers, setAnswers] = useState({});
  const [timeStarted, setTimeStarted] = useState(null);
  const [submitting, setSubmitting] = useState(false);

  useEffect(() => {
    loadQuiz();
  }, [quizId]);

  const loadQuiz = async () => {
    try {
      const quizData = await quizService.getQuiz(quizId);
      setQuiz(quizData);
      setTimeStarted(Date.now());
      
      // Initialize answers object
      const initialAnswers = {};
      quizData.questions.forEach(q => {
        initialAnswers[q.id] = '';
      });
      setAnswers(initialAnswers);
      
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleAnswerChange = (questionId, answer) => {
    setAnswers({
      ...answers,
      [questionId]: answer
    });
  };

  const goToQuestion = (index) => {
    setCurrentQuestion(index);
  };

  const nextQuestion = () => {
    if (currentQuestion < quiz.questions.length - 1) {
      setCurrentQuestion(currentQuestion + 1);
    }
  };

  const prevQuestion = () => {
    if (currentQuestion > 0) {
      setCurrentQuestion(currentQuestion - 1);
    }
  };

  const submitQuiz = async () => {
    const unanswered = Object.values(answers).filter(answer => !answer).length;
    
    if (unanswered > 0) {
      if (!window.confirm(`You have ${unanswered} unanswered questions. Submit anyway?`)) {
        return;
      }
    }

    setSubmitting(true);

    try {
      const timeElapsed = Math.floor((Date.now() - timeStarted) / 1000);
      
      const submission = {
        quiz_id: quizId,
        answers: Object.entries(answers).map(([questionId, selectedAnswer]) => ({
          question_id: questionId,
          selected_answer: selectedAnswer
        })),
        time_taken_seconds: timeElapsed
      };

      const result = await quizService.submitQuiz(submission);
      navigate(`/results/${result.result_id}`);
      
    } catch (err) {
      setError(err.message);
    } finally {
      setSubmitting(false);
    }
  };

  if (loading) {
    return (
      <div className="loading">
        <div className="spinner"></div>
        <p>Loading quiz...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="card">
        <div className="alert alert-error">
          {error}
        </div>
      </div>
    );
  }

  if (!quiz || quiz.questions.length === 0) {
    return (
      <div className="card">
        <div className="alert alert-info">
          <h3>Quiz is being generated...</h3>
          <p>Your quiz is still being generated by our AI. This usually takes 2-5 minutes.</p>
          <button 
            onClick={loadQuiz} 
            className="btn btn-primary mt-3"
          >
            Refresh
          </button>
        </div>
      </div>
    );
  }

  const currentQ = quiz.questions[currentQuestion];
  const progress = ((currentQuestion + 1) / quiz.questions.length) * 100;
  const answeredCount = Object.values(answers).filter(answer => answer).length;

  return (
    <div className="quiz-taker">
      {/* Quiz Header */}
      <div className="card quiz-header">
        <div className="quiz-info">
          <h2>📚 {quiz.topic}</h2>
          <div className="quiz-meta">
            <span className="difficulty difficulty-{quiz.difficulty.toLowerCase()}">
              {quiz.difficulty}
            </span>
            <span>{quiz.total_questions} Questions</span>
            <span>{answeredCount}/{quiz.questions.length} Answered</span>
          </div>
        </div>
        
        <div className="progress-bar">
          <div 
            className="progress-fill" 
            style={{ width: `${progress}%` }}
          ></div>
        </div>
      </div>

      {/* Question Navigation */}
      <div className="card question-nav">
        <h4>Question Navigation</h4>
        <div className="question-grid">
          {quiz.questions.map((q, index) => (
            <button
              key={q.id}
              onClick={() => goToQuestion(index)}
              className={`question-btn ${
                index === currentQuestion ? 'current' : ''
              } ${answers[q.id] ? 'answered' : ''}`}
            >
              {index + 1}
            </button>
          ))}
        </div>
      </div>

      {/* Current Question */}
      <div className="card question-card">
        <div className="question-header">
          <span className="question-number">
            Question {currentQuestion + 1} of {quiz.questions.length}
          </span>
        </div>
        
        <div className="question-content">
          <h3>{currentQ.question_text}</h3>
          
          <div className="options">
            {['A', 'B', 'C', 'D'].map(option => (
              <label key={option} className="option-label">
                <input
                  type="radio"
                  name={`question-${currentQ.id}`}
                  value={option}
                  checked={answers[currentQ.id] === option}
                  onChange={(e) => handleAnswerChange(currentQ.id, e.target.value)}
                />
                <span className="option-text">
                  <strong>{option}.</strong> {currentQ[`option_${option.toLowerCase()}`]}
                </span>
              </label>
            ))}
          </div>
        </div>

        <div className="question-actions">
          <button
            onClick={prevQuestion}
            disabled={currentQuestion === 0}
            className="btn btn-secondary"
          >
            ← Previous
          </button>
          
          <div className="center-actions">
            <button
              onClick={submitQuiz}
              disabled={submitting}
              className="btn btn-success"
            >
              {submitting ? (
                <>
                  <span className="spinner"></span>
                  Submitting...
                </>
              ) : (
                'Submit Quiz'
              )}
            </button>
          </div>
          
          <button
            onClick={nextQuestion}
            disabled={currentQuestion === quiz.questions.length - 1}
            className="btn btn-secondary"
          >
            Next →
          </button>
        </div>
      </div>

      <style jsx>{`
        .quiz-taker {
          max-width: 900px;
          margin: 0 auto;
          padding: 20px;
        }

        .quiz-header {
          margin-bottom: 1.5rem;
        }

        .quiz-info h2 {
          margin-bottom: 1rem;
          color: #333;
        }

        .quiz-meta {
          display: flex;
          gap: 1rem;
          margin-bottom: 1rem;
          flex-wrap: wrap;
        }

        .quiz-meta span {
          background: #f8f9fa;
          padding: 0.25rem 0.75rem;
          border-radius: 20px;
          font-size: 0.9rem;
        }

        .difficulty-easy { background: #d4edda; color: #155724; }
        .difficulty-medium { background: #fff3cd; color: #856404; }
        .difficulty-hard { background: #f8d7da; color: #721c24; }

        .progress-bar {
          width: 100%;
          height: 8px;
          background: #e9ecef;
          border-radius: 4px;
          overflow: hidden;
        }

        .progress-fill {
          height: 100%;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          transition: width 0.3s ease;
        }

        .question-nav {
          margin-bottom: 1.5rem;
        }

        .question-nav h4 {
          margin-bottom: 1rem;
          color: #333;
        }

        .question-grid {
          display: grid;
          grid-template-columns: repeat(auto-fill, minmax(50px, 1fr));
          gap: 0.5rem;
        }

        .question-btn {
          width: 50px;
          height: 50px;
          border: 2px solid #e9ecef;
          background: white;
          border-radius: 8px;
          cursor: pointer;
          font-weight: bold;
          transition: all 0.3s ease;
        }

        .question-btn:hover {
          border-color: #667eea;
        }

        .question-btn.current {
          border-color: #667eea;
          background: #667eea;
          color: white;
        }

        .question-btn.answered {
          border-color: #28a745;
          background: #28a745;
          color: white;
        }

        .question-btn.answered.current {
          border-color: #667eea;
          background: #667eea;
        }

        .question-card {
          margin-bottom: 1.5rem;
        }

        .question-header {
          margin-bottom: 1.5rem;
          padding-bottom: 1rem;
          border-bottom: 1px solid #e9ecef;
        }

        .question-number {
          background: #f8f9fa;
          padding: 0.5rem 1rem;
          border-radius: 20px;
          font-weight: bold;
          color: #666;
        }

        .question-content h3 {
          margin-bottom: 2rem;
          line-height: 1.6;
          color: #333;
        }

        .options {
          display: flex;
          flex-direction: column;
          gap: 1rem;
          margin-bottom: 2rem;
        }

        .option-label {
          display: flex;
          align-items: flex-start;
          gap: 1rem;
          padding: 1rem;
          border: 2px solid #e9ecef;
          border-radius: 8px;
          cursor: pointer;
          transition: all 0.3s ease;
        }

        .option-label:hover {
          border-color: #667eea;
          background: rgba(102, 126, 234, 0.05);
        }

        .option-label input[type="radio"] {
          margin-top: 0.25rem;
        }

        .option-label input[type="radio"]:checked + .option-text {
          color: #667eea;
          font-weight: bold;
        }

        .option-text {
          flex: 1;
          line-height: 1.5;
        }

        .question-actions {
          display: flex;
          justify-content: space-between;
          align-items: center;
          gap: 1rem;
        }

        .center-actions {
          flex: 1;
          display: flex;
          justify-content: center;
        }

        @media (max-width: 768px) {
          .quiz-meta {
            flex-direction: column;
            gap: 0.5rem;
          }
          
          .question-actions {
            flex-direction: column;
          }
          
          .center-actions {
            order: -1;
            margin-bottom: 1rem;
          }
        }
      `}</style>
    </div>
  );
};

export default QuizTaker;
