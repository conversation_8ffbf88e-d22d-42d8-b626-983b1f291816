# AI Quiz Generator Web App

A free, open-source AI-powered quiz generator that creates multiple choice questions from PDF textbooks using Hugging Face transformers.

## Features

- 📚 Upload PDF textbooks/eBooks
- 🎯 Select specific topics or keywords
- 📊 Choose difficulty levels (Easy, Medium, Hard)
- 🤖 AI-generated MCQ questions using Hugging Face models
- 💾 PostgreSQL database for result tracking
- 📱 React frontend with intuitive interface
- 🚀 FastAPI backend for high performance

## Tech Stack

### Frontend
- React 18
- Axios for API calls
- CSS3 for styling

### Backend
- FastAPI (Python)
- Hugging Face Transformers (T5/BART)
- PyPDF2/pdfplumber for PDF processing
- PostgreSQL for database
- SQLAlchemy ORM

### AI Models
- T5-base for question generation
- BART for text summarization

## Project Structure

```
quiz-generator/
├── frontend/                 # React application
│   ├── src/
│   ├── public/
│   └── package.json
├── backend/                  # FastAPI application
│   ├── app/
│   ├── models/
│   ├── database/
│   └── requirements.txt
├── database/                 # PostgreSQL schemas
│   ├── migrations/
│   └── init.sql
└── docs/                    # Documentation
```

## Prerequisites

- Python 3.8+
- Node.js 16+
- PostgreSQL 12+
- Git

## Installation & Setup

### 1. Clone Repository
```bash
git clone <repository-url>
cd quiz-generator
```

### 2. Database Setup
```bash
# Install PostgreSQL (if not installed)
# Windows: Download from https://www.postgresql.org/download/windows/
# macOS: brew install postgresql
# Linux: sudo apt-get install postgresql

# Create database
createdb quiz_generator

# Initialize database schema
psql -d quiz_generator -f database/init.sql
```

### 3. Backend Setup
```bash
cd backend

# Create virtual environment (recommended)
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Copy environment file
cp .env.example .env
# Edit .env file with your database credentials

# Start backend server
python -m uvicorn app.main:app --reload
```

### 4. Frontend Setup
```bash
cd frontend

# Install dependencies
npm install

# Start development server
npm start
```

### 5. Verify Setup
```bash
# Run setup verification script
python test_setup.py
```

## Usage

### Step 1: Register/Login
1. Open http://localhost:3000 in your browser
2. Register a new account or login with existing credentials

### Step 2: Generate Quiz
1. Click "Generate New Quiz"
2. Upload a PDF textbook (max 50MB)
3. Select topic/chapter or enter custom keyword
4. Choose difficulty level (Easy/Medium/Hard)
5. Set number of questions (10-100)
6. Click "Generate Quiz"

### Step 3: Take Quiz
1. Answer multiple choice questions
2. Navigate between questions
3. Submit when complete

### Step 4: View Results
1. See your score and grade
2. Review correct/incorrect answers
3. Read explanations for each question

## API Endpoints

### Authentication
- `POST /api/auth/register` - Register new user
- `POST /api/auth/login` - Login user
- `GET /api/auth/me` - Get current user

### File Upload
- `POST /api/upload/pdf` - Upload PDF file
- `GET /api/upload/pdf/{file_id}/info` - Get PDF info
- `DELETE /api/upload/pdf/{file_id}` - Delete PDF

### Quiz Management
- `POST /api/quiz/generate` - Generate new quiz
- `GET /api/quiz/{quiz_id}` - Get quiz questions
- `POST /api/quiz/submit` - Submit quiz answers
- `GET /api/quiz/results/{result_id}` - Get quiz results

## Troubleshooting

### Common Issues

1. **Database Connection Error**
   - Ensure PostgreSQL is running
   - Check database credentials in `.env`
   - Verify database exists: `psql -l | grep quiz_generator`

2. **AI Model Loading Issues**
   - First run downloads models (~500MB)
   - Ensure stable internet connection
   - Check available disk space

3. **PDF Processing Errors**
   - Use text-based PDFs (not scanned images)
   - Ensure PDF is not password protected
   - Check file size (max 50MB)

4. **Frontend Connection Issues**
   - Verify backend is running on port 8000
   - Check CORS settings in backend
   - Clear browser cache

### Performance Tips

- Use GPU if available for faster AI processing
- Smaller PDFs process faster
- Reduce number of questions for quicker generation

## Development

### Project Structure
```
quiz-generator/
├── backend/           # FastAPI backend
│   ├── app/          # API routes
│   ├── models/       # Database models
│   ├── utils/        # PDF processing & AI
│   └── database/     # DB connection
├── frontend/         # React frontend
│   ├── src/
│   │   ├── components/
│   │   └── services/
│   └── public/
├── database/         # SQL schemas
└── docs/            # Documentation
```

### Adding New Features

1. **New AI Models**: Update `utils/quiz_generator.py`
2. **New Question Types**: Modify database schema and frontend
3. **New File Formats**: Extend `utils/pdf_processor.py`

## Contributing

1. Fork the repository
2. Create feature branch
3. Make changes
4. Add tests
5. Submit pull request

## License

MIT License - Free for educational and commercial use.

## Support

For issues and questions:
- Check troubleshooting section
- Review API documentation
- Create GitHub issue with details
