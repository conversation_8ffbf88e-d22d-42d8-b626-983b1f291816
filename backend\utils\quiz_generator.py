"""
AI-powered quiz generation using Hugging Face transformers
"""
import os
import re
import random
from typing import List, Dict, Any
from transformers import (
    T5ForConditionalGeneration, 
    T5Tokenizer,
    BartForConditionalGeneration,
    BartTokenizer,
    pipeline
)
import torch
from nltk.tokenize import sent_tokenize
import nltk

class QuizGenerator:
    def __init__(self, model_name: str = "t5-base"):
        """Initialize quiz generator with specified model"""
        self.model_name = model_name
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        
        # Download NLTK data if needed
        try:
            nltk.data.find('tokenizers/punkt')
        except LookupError:
            nltk.download('punkt')
        
        # Initialize model and tokenizer
        self._load_model()
        
        # Question generation pipeline
        self.qg_pipeline = pipeline(
            "text2text-generation",
            model=self.model,
            tokenizer=self.tokenizer,
            device=0 if torch.cuda.is_available() else -1
        )
    
    def _load_model(self):
        """Load the specified model and tokenizer"""
        try:
            if "t5" in self.model_name.lower():
                self.model = T5ForConditionalGeneration.from_pretrained(self.model_name)
                self.tokenizer = T5Tokenizer.from_pretrained(self.model_name)
            elif "bart" in self.model_name.lower():
                self.model = BartForConditionalGeneration.from_pretrained(self.model_name)
                self.tokenizer = BartTokenizer.from_pretrained(self.model_name)
            else:
                # Default to T5
                self.model = T5ForConditionalGeneration.from_pretrained("t5-base")
                self.tokenizer = T5Tokenizer.from_pretrained("t5-base")
            
            self.model.to(self.device)
            print(f"✅ Model {self.model_name} loaded successfully on {self.device}")
            
        except Exception as e:
            print(f"❌ Failed to load model {self.model_name}: {e}")
            # Fallback to T5-base
            self.model = T5ForConditionalGeneration.from_pretrained("t5-base")
            self.tokenizer = T5Tokenizer.from_pretrained("t5-base")
            self.model.to(self.device)
            print("✅ Fallback to T5-base model loaded")
    
    def _chunk_text(self, text: str, max_length: int = 512) -> List[str]:
        """Split text into chunks suitable for the model"""
        sentences = sent_tokenize(text)
        chunks = []
        current_chunk = ""
        
        for sentence in sentences:
            # Check if adding this sentence would exceed max_length
            test_chunk = current_chunk + " " + sentence if current_chunk else sentence
            
            if len(self.tokenizer.encode(test_chunk)) <= max_length:
                current_chunk = test_chunk
            else:
                if current_chunk:
                    chunks.append(current_chunk.strip())
                current_chunk = sentence
        
        if current_chunk:
            chunks.append(current_chunk.strip())
        
        return chunks
    
    def _generate_question_from_text(self, text: str, difficulty: str = "Medium") -> Dict[str, Any]:
        """Generate a single question from text chunk"""
        try:
            # Create prompt based on difficulty
            if difficulty.lower() == "easy":
                prompt = f"generate easy question: {text}"
            elif difficulty.lower() == "hard":
                prompt = f"generate difficult question: {text}"
            else:
                prompt = f"generate question: {text}"
            
            # Generate question
            inputs = self.tokenizer.encode(prompt, return_tensors="pt", max_length=512, truncation=True)
            inputs = inputs.to(self.device)
            
            with torch.no_grad():
                outputs = self.model.generate(
                    inputs,
                    max_length=150,
                    num_return_sequences=1,
                    temperature=0.7,
                    do_sample=True,
                    pad_token_id=self.tokenizer.eos_token_id
                )
            
            question = self.tokenizer.decode(outputs[0], skip_special_tokens=True)
            
            # Generate options
            options, correct_letter = self._generate_options(text, question, difficulty)

            return {
                "question": question,
                "options": options,
                "correct_answer": correct_letter,
                "explanation": self._generate_explanation(text, question, options[0])
            }
            
        except Exception as e:
            print(f"Error generating question: {e}")
            return None
    
    def _generate_options(self, context: str, question: str, difficulty: str) -> List[str]:
        """Generate 4 multiple choice options"""
        try:
            # Generate correct answer
            answer_prompt = f"answer question: {question} context: {context}"
            inputs = self.tokenizer.encode(answer_prompt, return_tensors="pt", max_length=512, truncation=True)
            inputs = inputs.to(self.device)
            
            with torch.no_grad():
                outputs = self.model.generate(
                    inputs,
                    max_length=50,
                    num_return_sequences=1,
                    temperature=0.3,
                    do_sample=True,
                    pad_token_id=self.tokenizer.eos_token_id
                )
            
            correct_answer = self.tokenizer.decode(outputs[0], skip_special_tokens=True)
            
            # Generate distractors
            distractors = self._generate_distractors(context, question, correct_answer, difficulty)
            
            # Combine and shuffle
            options = [correct_answer] + distractors
            random.shuffle(options)
            
            # Find where correct answer ended up
            correct_index = options.index(correct_answer)
            correct_letter = chr(65 + correct_index)  # A, B, C, D
            
            return options, correct_letter
            
        except Exception as e:
            print(f"Error generating options: {e}")
            # Fallback options
            return [
                "Option A",
                "Option B", 
                "Option C",
                "Option D"
            ], "A"
    
    def _generate_distractors(self, context: str, question: str, correct_answer: str, difficulty: str) -> List[str]:
        """Generate plausible wrong answers"""
        distractors = []
        
        try:
            for i in range(3):  # Generate 3 distractors
                distractor_prompt = f"generate wrong answer for: {question} correct answer: {correct_answer} context: {context}"
                inputs = self.tokenizer.encode(distractor_prompt, return_tensors="pt", max_length=512, truncation=True)
                inputs = inputs.to(self.device)
                
                with torch.no_grad():
                    outputs = self.model.generate(
                        inputs,
                        max_length=50,
                        num_return_sequences=1,
                        temperature=0.8,
                        do_sample=True,
                        pad_token_id=self.tokenizer.eos_token_id
                    )
                
                distractor = self.tokenizer.decode(outputs[0], skip_special_tokens=True)
                if distractor and distractor != correct_answer:
                    distractors.append(distractor)
        
        except Exception as e:
            print(f"Error generating distractors: {e}")
        
        # Fill with generic distractors if needed
        while len(distractors) < 3:
            distractors.append(f"Alternative option {len(distractors) + 1}")
        
        return distractors[:3]
    
    def _generate_explanation(self, context: str, question: str, answer: str) -> str:
        """Generate explanation for the correct answer"""
        try:
            explain_prompt = f"explain why: {answer} is correct for question: {question} context: {context}"
            inputs = self.tokenizer.encode(explain_prompt, return_tensors="pt", max_length=512, truncation=True)
            inputs = inputs.to(self.device)
            
            with torch.no_grad():
                outputs = self.model.generate(
                    inputs,
                    max_length=100,
                    num_return_sequences=1,
                    temperature=0.5,
                    do_sample=True,
                    pad_token_id=self.tokenizer.eos_token_id
                )
            
            explanation = self.tokenizer.decode(outputs[0], skip_special_tokens=True)
            return explanation
            
        except Exception as e:
            print(f"Error generating explanation: {e}")
            return "This is the correct answer based on the provided context."
    
    def generate_quiz(self, text: str, topic: str, difficulty: str = "Medium", num_questions: int = 50) -> List[Dict[str, Any]]:
        """Generate a complete quiz from text"""
        print(f"🚀 Generating {num_questions} questions on topic: {topic} (Difficulty: {difficulty})")
        
        # Filter text by topic if specified
        if topic and topic.lower() != "full document":
            relevant_text = self._extract_topic_text(text, topic)
            if not relevant_text:
                relevant_text = text  # Fallback to full text
        else:
            relevant_text = text
        
        # Split text into chunks
        chunks = self._chunk_text(relevant_text)
        
        if not chunks:
            raise ValueError("No text chunks available for question generation")
        
        questions = []
        questions_per_chunk = max(1, num_questions // len(chunks))
        
        for i, chunk in enumerate(chunks):
            if len(questions) >= num_questions:
                break
                
            # Generate questions from this chunk
            chunk_questions = min(questions_per_chunk, num_questions - len(questions))
            
            for j in range(chunk_questions):
                question_data = self._generate_question_from_text(chunk, difficulty)
                
                if question_data:
                    options, correct_letter = self._generate_options(chunk, question_data["question"], difficulty)
                    
                    question_data.update({
                        "question_number": len(questions) + 1,
                        "options": options,
                        "correct_answer": correct_letter
                    })
                    
                    questions.append(question_data)
                    
                    if len(questions) % 10 == 0:
                        print(f"✅ Generated {len(questions)} questions...")
        
        print(f"🎉 Quiz generation complete! Generated {len(questions)} questions")
        return questions
    
    def _extract_topic_text(self, text: str, topic: str) -> str:
        """Extract text relevant to the specified topic"""
        sentences = sent_tokenize(text)
        relevant_sentences = []
        
        topic_lower = topic.lower()
        
        for sentence in sentences:
            if topic_lower in sentence.lower():
                relevant_sentences.append(sentence)
        
        return " ".join(relevant_sentences) if relevant_sentences else text
