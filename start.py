#!/usr/bin/env python3
"""
Startup script for AI Quiz Generator
Handles database initialization and starts both backend and frontend
"""
import os
import sys
import subprocess
import time
import signal
import threading
from pathlib import Path

class QuizGeneratorStarter:
    def __init__(self):
        self.backend_process = None
        self.frontend_process = None
        self.running = True
        
    def check_dependencies(self):
        """Check if all dependencies are installed"""
        print("🔍 Checking dependencies...")
        
        # Check Python packages
        try:
            import fastapi, uvicorn, sqlalchemy, transformers
            print("✅ Backend dependencies found")
        except ImportError as e:
            print(f"❌ Missing backend dependency: {e}")
            print("Run: cd backend && pip install -r requirements.txt")
            return False
        
        # Check Node.js and npm
        try:
            result = subprocess.run(['npm', '--version'], capture_output=True)
            if result.returncode == 0:
                print("✅ npm found")
            else:
                print("❌ npm not found")
                return False
        except FileNotFoundError:
            print("❌ npm not found")
            return False
        
        # Check if frontend dependencies are installed
        if not Path('frontend/node_modules').exists():
            print("❌ Frontend dependencies not installed")
            print("Run: cd frontend && npm install")
            return False
        else:
            print("✅ Frontend dependencies found")
        
        return True
    
    def setup_database(self):
        """Initialize database if needed"""
        print("🗄️ Setting up database...")
        
        # Check if database exists and is accessible
        try:
            from backend.database.connection import init_db
            init_db()
            print("✅ Database initialized")
            return True
        except Exception as e:
            print(f"❌ Database setup failed: {e}")
            print("Please ensure PostgreSQL is running and database 'quiz_generator' exists")
            return False
    
    def start_backend(self):
        """Start the FastAPI backend"""
        print("🚀 Starting backend server...")
        
        try:
            os.chdir('backend')
            self.backend_process = subprocess.Popen(
                [sys.executable, '-m', 'uvicorn', 'app.main:app', '--reload', '--port', '8000'],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )
            os.chdir('..')
            
            # Wait a bit and check if process is still running
            time.sleep(3)
            if self.backend_process.poll() is None:
                print("✅ Backend server started on http://localhost:8000")
                return True
            else:
                print("❌ Backend server failed to start")
                return False
                
        except Exception as e:
            print(f"❌ Failed to start backend: {e}")
            return False
    
    def start_frontend(self):
        """Start the React frontend"""
        print("⚛️ Starting frontend server...")
        
        try:
            os.chdir('frontend')
            
            # Set environment variable to avoid browser auto-opening
            env = os.environ.copy()
            env['BROWSER'] = 'none'
            
            self.frontend_process = subprocess.Popen(
                ['npm', 'start'],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                env=env
            )
            os.chdir('..')
            
            # Wait a bit for startup
            time.sleep(5)
            if self.frontend_process.poll() is None:
                print("✅ Frontend server started on http://localhost:3000")
                return True
            else:
                print("❌ Frontend server failed to start")
                return False
                
        except Exception as e:
            print(f"❌ Failed to start frontend: {e}")
            return False
    
    def monitor_processes(self):
        """Monitor backend and frontend processes"""
        while self.running:
            time.sleep(5)
            
            # Check backend
            if self.backend_process and self.backend_process.poll() is not None:
                print("⚠️ Backend process stopped")
                break
            
            # Check frontend
            if self.frontend_process and self.frontend_process.poll() is not None:
                print("⚠️ Frontend process stopped")
                break
    
    def stop_processes(self):
        """Stop all processes"""
        print("\n🛑 Stopping servers...")
        self.running = False
        
        if self.backend_process:
            self.backend_process.terminate()
            self.backend_process.wait()
            print("✅ Backend stopped")
        
        if self.frontend_process:
            self.frontend_process.terminate()
            self.frontend_process.wait()
            print("✅ Frontend stopped")
    
    def signal_handler(self, signum, frame):
        """Handle Ctrl+C gracefully"""
        self.stop_processes()
        sys.exit(0)
    
    def run(self):
        """Main startup sequence"""
        print("🤖 AI Quiz Generator - Startup")
        print("=" * 40)
        
        # Set up signal handler for graceful shutdown
        signal.signal(signal.SIGINT, self.signal_handler)
        
        # Check dependencies
        if not self.check_dependencies():
            return False
        
        # Setup database
        if not self.setup_database():
            return False
        
        # Start backend
        if not self.start_backend():
            return False
        
        # Start frontend
        if not self.start_frontend():
            self.stop_processes()
            return False
        
        print("\n🎉 AI Quiz Generator is running!")
        print("=" * 40)
        print("📱 Frontend: http://localhost:3000")
        print("🔧 Backend API: http://localhost:8000")
        print("📚 API Docs: http://localhost:8000/docs")
        print("\nPress Ctrl+C to stop all servers")
        print("=" * 40)
        
        # Monitor processes
        try:
            self.monitor_processes()
        except KeyboardInterrupt:
            pass
        finally:
            self.stop_processes()
        
        return True

def main():
    """Entry point"""
    starter = QuizGeneratorStarter()
    success = starter.run()
    
    if not success:
        print("\n❌ Startup failed. Please check the errors above.")
        print("\nFor help, see README.md or run: python test_setup.py")
        sys.exit(1)

if __name__ == "__main__":
    main()
