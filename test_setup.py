#!/usr/bin/env python3
"""
Test script to verify the AI Quiz Generator setup
"""
import os
import sys
import subprocess
import requests
import time

def check_python_version():
    """Check if Python version is compatible"""
    print("🐍 Checking Python version...")
    version = sys.version_info
    if version.major == 3 and version.minor >= 8:
        print(f"✅ Python {version.major}.{version.minor}.{version.micro} - Compatible")
        return True
    else:
        print(f"❌ Python {version.major}.{version.minor}.{version.micro} - Requires Python 3.8+")
        return False

def check_node_version():
    """Check if Node.js is installed"""
    print("\n📦 Checking Node.js version...")
    try:
        result = subprocess.run(['node', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            version = result.stdout.strip()
            print(f"✅ Node.js {version} - Available")
            return True
        else:
            print("❌ Node.js not found")
            return False
    except FileNotFoundError:
        print("❌ Node.js not found")
        return False

def check_postgresql():
    """Check if PostgreSQL is available"""
    print("\n🐘 Checking PostgreSQL...")
    try:
        result = subprocess.run(['psql', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            version = result.stdout.strip()
            print(f"✅ PostgreSQL - Available")
            return True
        else:
            print("❌ PostgreSQL not found")
            return False
    except FileNotFoundError:
        print("❌ PostgreSQL not found")
        return False

def check_backend_dependencies():
    """Check if backend dependencies can be imported"""
    print("\n🔧 Checking backend dependencies...")
    
    dependencies = [
        'fastapi',
        'uvicorn',
        'sqlalchemy',
        'psycopg2',
        'transformers',
        'torch',
        'PyPDF2',
        'pdfplumber',
        'nltk',
        'passlib',
        'jose'
    ]
    
    missing = []
    for dep in dependencies:
        try:
            __import__(dep)
            print(f"✅ {dep}")
        except ImportError:
            print(f"❌ {dep} - Missing")
            missing.append(dep)
    
    return len(missing) == 0

def check_frontend_dependencies():
    """Check if frontend dependencies are installed"""
    print("\n⚛️ Checking frontend dependencies...")
    
    if os.path.exists('frontend/node_modules'):
        print("✅ Frontend dependencies installed")
        return True
    else:
        print("❌ Frontend dependencies not installed")
        return False

def test_backend_startup():
    """Test if backend can start"""
    print("\n🚀 Testing backend startup...")
    
    # Change to backend directory
    os.chdir('backend')
    
    try:
        # Start backend process
        process = subprocess.Popen(
            [sys.executable, '-m', 'uvicorn', 'app.main:app', '--port', '8001'],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )
        
        # Wait a bit for startup
        time.sleep(5)
        
        # Test health endpoint
        try:
            response = requests.get('http://localhost:8001/health', timeout=5)
            if response.status_code == 200:
                print("✅ Backend started successfully")
                success = True
            else:
                print(f"❌ Backend health check failed: {response.status_code}")
                success = False
        except requests.exceptions.RequestException as e:
            print(f"❌ Backend connection failed: {e}")
            success = False
        
        # Terminate process
        process.terminate()
        process.wait()
        
        return success
        
    except Exception as e:
        print(f"❌ Backend startup failed: {e}")
        return False
    finally:
        os.chdir('..')

def main():
    """Run all tests"""
    print("🤖 AI Quiz Generator - Setup Verification")
    print("=" * 50)
    
    tests = [
        ("Python Version", check_python_version),
        ("Node.js", check_node_version),
        ("PostgreSQL", check_postgresql),
        ("Backend Dependencies", check_backend_dependencies),
        ("Frontend Dependencies", check_frontend_dependencies),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test failed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("📋 SETUP VERIFICATION SUMMARY")
    print("=" * 50)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:<25} {status}")
        if result:
            passed += 1
    
    print(f"\nPassed: {passed}/{len(results)} tests")
    
    if passed == len(results):
        print("\n🎉 All tests passed! Your setup is ready.")
        print("\nNext steps:")
        print("1. Start PostgreSQL and create database 'quiz_generator'")
        print("2. Run: cd backend && python -m uvicorn app.main:app --reload")
        print("3. Run: cd frontend && npm start")
    else:
        print("\n⚠️  Some tests failed. Please fix the issues above.")
        print("\nInstallation commands:")
        print("Backend: cd backend && pip install -r requirements.txt")
        print("Frontend: cd frontend && npm install")

if __name__ == "__main__":
    main()
