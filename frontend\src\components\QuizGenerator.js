import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { quizService } from '../services/quizService';

const QuizGenerator = ({ user }) => {
  const navigate = useNavigate();
  const [step, setStep] = useState(1);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  
  // Form data
  const [file, setFile] = useState(null);
  const [fileInfo, setFileInfo] = useState(null);
  const [quizData, setQuizData] = useState({
    topic: '',
    difficulty: 'Medium',
    num_questions: 50
  });

  const handleFileChange = (e) => {
    const selectedFile = e.target.files[0];
    if (selectedFile) {
      if (selectedFile.type !== 'application/pdf') {
        setError('Please select a PDF file');
        return;
      }
      if (selectedFile.size > 50 * 1024 * 1024) { // 50MB
        setError('File size must be less than 50MB');
        return;
      }
      setFile(selectedFile);
      setError('');
    }
  };

  const handleUpload = async () => {
    if (!file) {
      setError('Please select a PDF file');
      return;
    }

    setLoading(true);
    setError('');

    try {
      const uploadResult = await quizService.uploadPDF(file);
      setFileInfo(uploadResult);
      setSuccess('PDF uploaded successfully!');
      setStep(2);
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleQuizDataChange = (e) => {
    setQuizData({
      ...quizData,
      [e.target.name]: e.target.value
    });
  };

  const handleGenerateQuiz = async () => {
    setLoading(true);
    setError('');

    try {
      const generateData = {
        file_id: fileInfo.file_id,
        topic: quizData.topic || 'Full Document',
        difficulty: quizData.difficulty,
        num_questions: parseInt(quizData.num_questions)
      };

      const quiz = await quizService.generateQuiz(generateData);
      setSuccess('Quiz generation started! Redirecting...');
      
      // Redirect to quiz page after a short delay
      setTimeout(() => {
        navigate(`/quiz/${quiz.quiz_id}`);
      }, 2000);
      
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="quiz-generator">
      <div className="card">
        <h1>🤖 Generate AI Quiz</h1>
        <p>Create intelligent quizzes from your PDF textbooks</p>

        {/* Progress indicator */}
        <div className="progress-indicator">
          <div className={`step-indicator ${step >= 1 ? 'active' : ''}`}>
            <span>1</span>
            <label>Upload PDF</label>
          </div>
          <div className={`step-indicator ${step >= 2 ? 'active' : ''}`}>
            <span>2</span>
            <label>Configure Quiz</label>
          </div>
          <div className={`step-indicator ${step >= 3 ? 'active' : ''}`}>
            <span>3</span>
            <label>Generate</label>
          </div>
        </div>

        {error && (
          <div className="alert alert-error">
            {error}
          </div>
        )}

        {success && (
          <div className="alert alert-success">
            {success}
          </div>
        )}

        {/* Step 1: Upload PDF */}
        {step === 1 && (
          <div className="step-content">
            <h3>Step 1: Upload Your PDF Textbook</h3>
            
            <div className="upload-area">
              <input
                type="file"
                accept=".pdf"
                onChange={handleFileChange}
                className="file-input"
                id="pdf-upload"
              />
              <label htmlFor="pdf-upload" className="upload-label">
                <div className="upload-icon">📄</div>
                <div>
                  {file ? (
                    <>
                      <strong>{file.name}</strong>
                      <br />
                      <small>{(file.size / (1024 * 1024)).toFixed(2)} MB</small>
                    </>
                  ) : (
                    <>
                      <strong>Click to upload PDF</strong>
                      <br />
                      <small>Or drag and drop your textbook here</small>
                    </>
                  )}
                </div>
              </label>
            </div>

            <div className="upload-info">
              <h4>📋 Requirements:</h4>
              <ul>
                <li>PDF format only</li>
                <li>Maximum file size: 50MB</li>
                <li>Text-based PDFs (not scanned images)</li>
                <li>Academic textbooks work best</li>
              </ul>
            </div>

            <button
              onClick={handleUpload}
              disabled={!file || loading}
              className="btn btn-primary"
            >
              {loading ? (
                <>
                  <span className="spinner"></span>
                  Uploading...
                </>
              ) : (
                'Upload & Process PDF'
              )}
            </button>
          </div>
        )}

        {/* Step 2: Configure Quiz */}
        {step === 2 && fileInfo && (
          <div className="step-content">
            <h3>Step 2: Configure Your Quiz</h3>
            
            <div className="file-summary">
              <h4>📄 Uploaded File: {fileInfo.filename}</h4>
              <p>Size: {(fileInfo.size / (1024 * 1024)).toFixed(2)} MB</p>
            </div>

            <div className="form-group">
              <label className="form-label">📚 Topic/Chapter</label>
              <select
                name="topic"
                value={quizData.topic}
                onChange={handleQuizDataChange}
                className="form-select"
              >
                <option value="">Full Document</option>
                {fileInfo.chapters && fileInfo.chapters.map((chapter, index) => (
                  <option key={index} value={chapter}>
                    {chapter}
                  </option>
                ))}
              </select>
              <small>Or enter a custom keyword:</small>
              <input
                type="text"
                name="topic"
                value={quizData.topic}
                onChange={handleQuizDataChange}
                className="form-control"
                placeholder="e.g., photosynthesis, calculus, history"
              />
            </div>

            <div className="form-group">
              <label className="form-label">⚡ Difficulty Level</label>
              <select
                name="difficulty"
                value={quizData.difficulty}
                onChange={handleQuizDataChange}
                className="form-select"
              >
                <option value="Easy">Easy - Basic concepts and definitions</option>
                <option value="Medium">Medium - Application and analysis</option>
                <option value="Hard">Hard - Complex problem solving</option>
              </select>
            </div>

            <div className="form-group">
              <label className="form-label">🔢 Number of Questions</label>
              <select
                name="num_questions"
                value={quizData.num_questions}
                onChange={handleQuizDataChange}
                className="form-select"
              >
                <option value="10">10 Questions (Quick Quiz)</option>
                <option value="25">25 Questions (Medium Quiz)</option>
                <option value="50">50 Questions (Full Quiz)</option>
                <option value="100">100 Questions (Comprehensive)</option>
              </select>
            </div>

            {fileInfo.keywords && fileInfo.keywords.length > 0 && (
              <div className="keywords-section">
                <h4>🏷️ Detected Keywords:</h4>
                <div className="keywords">
                  {fileInfo.keywords.slice(0, 20).map((keyword, index) => (
                    <span
                      key={index}
                      className="keyword-tag"
                      onClick={() => setQuizData({...quizData, topic: keyword})}
                    >
                      {keyword}
                    </span>
                  ))}
                </div>
              </div>
            )}

            <div className="step-actions">
              <button
                onClick={() => setStep(1)}
                className="btn btn-secondary"
              >
                ← Back
              </button>
              <button
                onClick={handleGenerateQuiz}
                disabled={loading}
                className="btn btn-primary"
              >
                {loading ? (
                  <>
                    <span className="spinner"></span>
                    Generating Quiz...
                  </>
                ) : (
                  'Generate Quiz 🚀'
                )}
              </button>
            </div>
          </div>
        )}
      </div>

      <style jsx>{`
        .quiz-generator {
          max-width: 800px;
          margin: 0 auto;
          padding: 20px;
        }

        .quiz-generator h1 {
          text-align: center;
          margin-bottom: 0.5rem;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }

        .quiz-generator > .card > p {
          text-align: center;
          color: #666;
          margin-bottom: 2rem;
        }

        .progress-indicator {
          display: flex;
          justify-content: center;
          margin-bottom: 3rem;
          gap: 2rem;
        }

        .step-indicator {
          display: flex;
          flex-direction: column;
          align-items: center;
          opacity: 0.5;
          transition: opacity 0.3s ease;
        }

        .step-indicator.active {
          opacity: 1;
        }

        .step-indicator span {
          width: 40px;
          height: 40px;
          border-radius: 50%;
          background: #e9ecef;
          display: flex;
          align-items: center;
          justify-content: center;
          font-weight: bold;
          margin-bottom: 0.5rem;
        }

        .step-indicator.active span {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          color: white;
        }

        .step-indicator label {
          font-size: 0.9rem;
          color: #666;
        }

        .upload-area {
          margin: 2rem 0;
        }

        .file-input {
          display: none;
        }

        .upload-label {
          display: flex;
          flex-direction: column;
          align-items: center;
          padding: 3rem;
          border: 2px dashed #ccc;
          border-radius: 12px;
          cursor: pointer;
          transition: all 0.3s ease;
          background: #f8f9fa;
        }

        .upload-label:hover {
          border-color: #667eea;
          background: rgba(102, 126, 234, 0.05);
        }

        .upload-icon {
          font-size: 3rem;
          margin-bottom: 1rem;
        }

        .upload-info {
          background: #f8f9fa;
          padding: 1.5rem;
          border-radius: 8px;
          margin: 1.5rem 0;
        }

        .upload-info h4 {
          margin-bottom: 1rem;
          color: #333;
        }

        .upload-info ul {
          margin: 0;
          padding-left: 1.5rem;
        }

        .upload-info li {
          margin-bottom: 0.5rem;
          color: #666;
        }

        .file-summary {
          background: #e8f5e8;
          padding: 1rem;
          border-radius: 8px;
          margin-bottom: 2rem;
        }

        .keywords-section {
          margin: 2rem 0;
        }

        .keywords {
          display: flex;
          flex-wrap: wrap;
          gap: 0.5rem;
          margin-top: 1rem;
        }

        .keyword-tag {
          background: #e9ecef;
          padding: 0.25rem 0.75rem;
          border-radius: 20px;
          font-size: 0.9rem;
          cursor: pointer;
          transition: background-color 0.3s ease;
        }

        .keyword-tag:hover {
          background: #667eea;
          color: white;
        }

        .step-actions {
          display: flex;
          justify-content: space-between;
          margin-top: 2rem;
        }

        @media (max-width: 768px) {
          .progress-indicator {
            gap: 1rem;
          }
          
          .step-actions {
            flex-direction: column;
            gap: 1rem;
          }
        }
      `}</style>
    </div>
  );
};

export default QuizGenerator;
