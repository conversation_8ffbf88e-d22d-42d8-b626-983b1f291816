# AI Quiz Generator - Deployment Guide

## 🚀 Quick Start (Automated)

### Option 1: Automated Installation
```bash
# Run the automated installer
python install.py

# Start the application
python start.py
```

### Option 2: Manual Installation
Follow the detailed steps below for manual setup.

## 📋 Prerequisites

### Required Software
- **Python 3.8+** - [Download](https://www.python.org/downloads/)
- **Node.js 16+** - [Download](https://nodejs.org/)
- **PostgreSQL 12+** - [Download](https://www.postgresql.org/download/)
- **Git** - [Download](https://git-scm.com/)

### System Requirements
- **RAM**: 4GB minimum, 8GB recommended
- **Storage**: 2GB free space (for AI models)
- **Internet**: Required for initial model download

## 🛠️ Manual Installation Steps

### 1. Database Setup
```bash
# Start PostgreSQL service
# Windows: Start from Services or pgAdmin
# macOS: brew services start postgresql
# Linux: sudo systemctl start postgresql

# Create database
createdb quiz_generator

# Initialize schema
psql -d quiz_generator -f database/init.sql
```

### 2. Backend Setup
```bash
cd backend

# Create virtual environment
python -m venv venv

# Activate virtual environment
# Windows:
venv\Scripts\activate
# macOS/Linux:
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt

# Setup environment
cp .env.example .env
# Edit .env with your database credentials

# Test backend
python -m uvicorn app.main:app --reload
```

### 3. Frontend Setup
```bash
cd frontend

# Install dependencies
npm install

# Start development server
npm start
```

## 🔧 Configuration

### Environment Variables (.env)
```bash
# Database
DATABASE_URL=postgresql://username:password@localhost:5432/quiz_generator

# API Settings
API_HOST=0.0.0.0
API_PORT=8000
DEBUG=True

# Security
SECRET_KEY=your-secret-key-here

# AI Model
HUGGINGFACE_MODEL_NAME=t5-base

# File Upload
MAX_FILE_SIZE_MB=50
UPLOAD_FOLDER=uploads
```

### Database Configuration
- Default database name: `quiz_generator`
- Default user: `postgres`
- Default port: `5432`

## 🚦 Running the Application

### Development Mode
```bash
# Terminal 1: Backend
cd backend
python -m uvicorn app.main:app --reload

# Terminal 2: Frontend
cd frontend
npm start
```

### Production Mode
```bash
# Backend
cd backend
python -m uvicorn app.main:app --host 0.0.0.0 --port 8000

# Frontend (build and serve)
cd frontend
npm run build
# Serve build folder with your preferred web server
```

## 🧪 Testing

### Verify Installation
```bash
python test_setup.py
```

### Manual Testing
1. Open http://localhost:3000
2. Register a new account
3. Upload a sample PDF
4. Generate a quiz
5. Take the quiz
6. View results

## 🐛 Troubleshooting

### Common Issues

#### Database Connection Error
```bash
# Check PostgreSQL status
pg_isready

# Check database exists
psql -l | grep quiz_generator

# Recreate database if needed
dropdb quiz_generator
createdb quiz_generator
psql -d quiz_generator -f database/init.sql
```

#### AI Model Download Issues
```bash
# Clear cache and retry
rm -rf ~/.cache/huggingface/
python -c "from transformers import T5ForConditionalGeneration; T5ForConditionalGeneration.from_pretrained('t5-base')"
```

#### Port Already in Use
```bash
# Find and kill process using port 8000
# Windows:
netstat -ano | findstr :8000
taskkill /PID <PID> /F

# macOS/Linux:
lsof -ti:8000 | xargs kill -9
```

#### Frontend Build Issues
```bash
cd frontend
rm -rf node_modules package-lock.json
npm install
npm start
```

## 📊 Performance Optimization

### AI Model Performance
- Use GPU if available (install `torch` with CUDA support)
- Reduce batch size if running out of memory
- Use smaller models for faster generation

### Database Performance
- Add indexes for frequently queried columns
- Use connection pooling for production
- Regular database maintenance

### Frontend Performance
- Build for production: `npm run build`
- Use CDN for static assets
- Enable gzip compression

## 🔒 Security Considerations

### Production Deployment
1. Change default secret key
2. Use environment variables for sensitive data
3. Enable HTTPS
4. Set up proper CORS policies
5. Use strong database passwords
6. Regular security updates

### File Upload Security
- Validate file types and sizes
- Scan uploaded files for malware
- Limit upload frequency per user
- Store files outside web root

## 📈 Scaling

### Horizontal Scaling
- Use load balancer for multiple backend instances
- Separate database server
- Use Redis for session storage
- Implement file storage service (AWS S3, etc.)

### Vertical Scaling
- Increase server resources
- Optimize database queries
- Use caching (Redis/Memcached)
- Optimize AI model inference

## 🔄 Updates and Maintenance

### Updating the Application
```bash
# Backup database
pg_dump quiz_generator > backup.sql

# Pull latest changes
git pull origin main

# Update backend dependencies
cd backend
pip install -r requirements.txt

# Update frontend dependencies
cd frontend
npm install

# Run migrations if any
# Check for new migration files in database/migrations/

# Restart services
```

### Regular Maintenance
- Monitor disk space (AI models and uploads)
- Clean up old uploaded files
- Monitor database performance
- Update dependencies regularly
- Backup database regularly

## 📞 Support

### Getting Help
1. Check this deployment guide
2. Review README.md
3. Run diagnostic script: `python test_setup.py`
4. Check application logs
5. Create GitHub issue with details

### Log Locations
- Backend logs: Console output or log files
- Frontend logs: Browser console
- Database logs: PostgreSQL log directory
- System logs: OS-specific locations

## 📝 Additional Resources

- [FastAPI Documentation](https://fastapi.tiangolo.com/)
- [React Documentation](https://reactjs.org/docs/)
- [PostgreSQL Documentation](https://www.postgresql.org/docs/)
- [Hugging Face Transformers](https://huggingface.co/docs/transformers/)

---

**Note**: This application is designed for educational use. For production deployment, implement additional security measures and performance optimizations.
